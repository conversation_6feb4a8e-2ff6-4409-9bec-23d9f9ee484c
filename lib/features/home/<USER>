import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../time_box/timebox_list_page.dart';
import '../time_box/providers/timebox_provider.dart';
import '../daily_plan/models/daily_plan.dart';
import '../daily_plan/notifiers/daily_plan_notifier.dart';
import '../study_time/providers/study_time_providers.dart';
import '../../services/global_timer_service.dart';
import '../../services/app_initialization_service.dart';
import '../achievement/providers/achievement_provider.dart';
import '../../l10n/app_localizations.dart';
import '../achievement/widgets/achievement_unlock_notification.dart';
import '../../providers/navigation_provider.dart';

/// 标签页切换通知
class TabSwitchNotification extends Notification {
  final int tabIndex;
  const TabSwitchNotification(this.tabIndex);
}

/// 首页 - 应用核心入口
class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> {
  // 全局计时器服务
  final GlobalTimerService _globalTimerService = GlobalTimerService();

  // 防止重复刷新的时间戳
  DateTime? _lastSilentRefreshTime;

  @override
  void initState() {
    super.initState();

    // 监听全局计时器状态变化
    _globalTimerService.addListener(_onGlobalTimerStateChanged);

    // 初始化成就系统集成
    _initializeAchievementSystem();

    // 触发静默后台刷新
    _triggerSilentRefreshOnPageAccess();
  }

  /// 初始化成就系统集成
  void _initializeAchievementSystem() {
    // 这里会在build方法中通过ref获取服务并设置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 延迟到下一帧执行，确保Provider已经初始化
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // 检测路由变化，如果是首次访问或重新访问首页，触发静默刷新
    final currentRoute = GoRouterState.of(context).uri.path;
    if (currentRoute == '/home') {
      // 延迟执行，避免在构建过程中触发状态更新
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _performSilentRefresh().catchError((error) {
            print('❌ 路由变化触发的静默刷新失败: $error');
          });
        }
      });
    }
  }

  @override
  void dispose() {
    // 移除全局计时器监听器
    _globalTimerService.removeListener(_onGlobalTimerStateChanged);
    super.dispose();
  }

  /// 全局计时器状态变化回调
  void _onGlobalTimerStateChanged() {
    if (mounted) {
      // 这里不需要setState，因为主页不显示计时器UI
      // 但需要确保浮动窗口能正确更新
      print('🏠 主页监听到全局计时器状态变化');
    }
  }

  /// 触发静默后台刷新（当页面被访问时）
  void _triggerSilentRefreshOnPageAccess() {
    // 使用 WidgetsBinding.instance.addPostFrameCallback 确保在构建完成后执行
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 延迟一小段时间，确保页面完全加载
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          // 在后台静默刷新数据，不阻塞UI
          _performSilentRefresh().catchError((error) {
            // 静默处理错误，不影响用户体验
            print('❌ 页面访问时的静默刷新失败: $error');
          });
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        // 初始化应用服务
        ref.watch(appInitializationServiceProvider);

        // 监听导航触发的刷新
        ref.listen<bool>(homeRefreshTriggerProvider, (previous, shouldRefresh) {
          if (shouldRefresh && mounted) {
            print('🔄 检测到导航触发的首页刷新请求');
            // 清除刷新标志
            ref.read(navigationProvider.notifier).clearRefreshFlag();
            // 执行静默刷新
            _performSilentRefresh().catchError((error) {
              print('❌ 导航触发的静默刷新失败: $error');
            });
          }
        });

        // 初始化成就系统
        final achievementTriggerService = ref.watch(
          achievementTriggerServiceProvider,
        );
        _globalTimerService.setAchievementTriggerService(
          achievementTriggerService,
        );

        // 设置成就解锁通知
        ref
            .read(achievementProvider.notifier)
            .onAchievementUnlocked = (achievement) {
          if (mounted) {
            AchievementUnlockNotification.show(
              context,
              achievement,
              onDismiss: () {
                // 成就解锁通知关闭后的回调
                print('🎉 成就解锁通知已关闭: ${achievement.name}');
              },
            );
          }
        };

        return Scaffold(
          backgroundColor: const Color(0xFFFAFAFA),
          appBar: _buildAppBar(),
          body: RefreshIndicator(
            onRefresh: _handleRefresh,
            color: const Color(0xFF2E7EED),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildQuickActions(),
                  const SizedBox(height: 24),
                  _buildTodayLearningPlan(),
                  const SizedBox(height: 24),
                  _buildTodayStats(),
                  const SizedBox(height: 16),
                  // 调试区域（仅在debug模式显示）
                  if (kDebugMode) ...[
                    _buildDebugSection(),
                    const SizedBox(height: 16),
                  ],
                  const SizedBox(height: 100), // 底部留白
                ],
              ),
            ),
          ),
          floatingActionButton: _buildFloatingActionButton(),
        );
      },
    );
  }

  /// 构建应用栏
  AppBar _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      foregroundColor: const Color(0xFF37352F),
      elevation: 0,
      title: Row(
        children: [
          // const OneDayLogoSimple(size: 28),
          ClipRRect(
            borderRadius: BorderRadius.circular(8.0),
            child: Image.asset(
              'assets/icons/app_icon.png',
              width: 32,
              height: 32,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'OneDay',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF37352F),
                  ),
                ),
                Text(
                  '怕一天浪费，就用OneDay',
                  style: TextStyle(
                    fontSize: 12,
                    color: const Color(0xFF787774),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(1),
        child: Container(
          height: 1,
          color: const Color(0xFF37352F).withValues(alpha: 0.1),
        ),
      ),
    );
  }

  /// 构建今日学习计划
  Widget _buildTodayLearningPlan() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '今日学习计划',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.04),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              LearningPlanItem(
                title: '每日计划',
                description: '制定每日学习计划',
                icon: Icons.assignment,
                color: const Color(0xFF2E7EED),
                type: DailyPlanType.planning,
              ),
              const SizedBox(height: 16),
              LearningPlanItem(
                title: '每日优化',
                description: '每日学习效果优化',
                icon: Icons.trending_up,
                color: const Color(0xFF0F7B6C),
                type: DailyPlanType.optimization,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建今日统计
  Widget _buildTodayStats() {
    return Consumer(
      builder: (context, ref, child) {
        print('🏠 _buildTodayStats Consumer：开始重建');

        final studySummary = ref.watch(todayStudySummaryProvider);

        // 调试：打印当前统计数据
        print('🏠 首页统计数据更新: $studySummary');
        print('🏠 Consumer重建完成，返回UI组件');

        final statsData = [
          {
            'title': '学习时长',
            'value': studySummary['studyTime'] ?? '0m',
            'subtitle': '今日已学习',
            'color': const Color(0xFF2E7EED),
            'icon': Icons.schedule,
          },
          {
            'title': '虚拟工资',
            'value': studySummary['wage'] ?? '¥0',
            'subtitle': '今日收入',
            'color': const Color(0xFF0F7B6C),
            'icon': Icons.attach_money,
          },
          {
            'title': '完成任务',
            'value': studySummary['completedTasks'] ?? '0/0',
            'subtitle': '时间盒子',
            'color': const Color(0xFF7C3AED),
            'icon': Icons.task_alt,
          },
          {
            'title': '连续天数',
            'value': studySummary['streakDays'] ?? '0天',
            'subtitle': '学习打卡',
            'color': const Color(0xFFD9730D),
            'icon': Icons.local_fire_department,
          },
          {
            'title': '学习ROI',
            'value': studySummary['learningROI'] ?? '0.0',
            'subtitle': '今日效率',
            'color': const Color(0xFF9B59B6),
            'icon': Icons.trending_up,
          },
          {
            'title': '并行时间',
            'value': '+${studySummary['parallelTime'] ?? '0分钟'}',
            'subtitle': '时间收益',
            'color': const Color(0xFF16A085),
            'icon': Icons.schedule_outlined,
          },
          {
            'title': '信噪比',
            'value':
                '${studySummary['focusRatio'] ?? '0.0'} (${studySummary['focusLevel'] ?? '需改进'})',
            'subtitle': '专注度',
            'color': const Color(0xFFE67E22),
            'icon': Icons.psychology,
          },
        ];

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '今日概览',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 12),
            // 使用 LayoutBuilder 实现响应式统计卡片布局
            LayoutBuilder(
              builder: (context, constraints) {
                // 根据可用宽度决定布局方式
                if (constraints.maxWidth > 1200) {
                  // 超宽屏：7列布局
                  return Row(
                    children: statsData
                        .map(
                          (stat) => Expanded(
                            child: Padding(
                              padding: EdgeInsets.only(
                                right: stat == statsData.last ? 0 : 8,
                              ),
                              child: StatsCard(
                                title: stat['title'] as String,
                                value: stat['value'] as String,
                                subtitle: stat['subtitle'] as String,
                                color: stat['color'] as Color,
                                icon: stat['icon'] as IconData,
                              ),
                            ),
                          ),
                        )
                        .toList(),
                  );
                } else if (constraints.maxWidth > 800) {
                  // 宽屏：4+3布局
                  return Column(
                    children: [
                      Row(
                        children: statsData
                            .take(4)
                            .map(
                              (stat) => Expanded(
                                child: Padding(
                                  padding: EdgeInsets.only(
                                    right:
                                        statsData
                                                .take(4)
                                                .toList()
                                                .indexOf(stat) ==
                                            3
                                        ? 0
                                        : 12,
                                  ),
                                  child: StatsCard(
                                    title: stat['title'] as String,
                                    value: stat['value'] as String,
                                    subtitle: stat['subtitle'] as String,
                                    color: stat['color'] as Color,
                                    icon: stat['icon'] as IconData,
                                  ),
                                ),
                              ),
                            )
                            .toList(),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          ...statsData
                              .skip(4)
                              .map(
                                (stat) => Expanded(
                                  child: Padding(
                                    padding: EdgeInsets.only(
                                      right:
                                          statsData
                                                  .skip(4)
                                                  .toList()
                                                  .indexOf(stat) ==
                                              2
                                          ? 0
                                          : 12,
                                    ),
                                    child: StatsCard(
                                      title: stat['title'] as String,
                                      value: stat['value'] as String,
                                      subtitle: stat['subtitle'] as String,
                                      color: stat['color'] as Color,
                                      icon: stat['icon'] as IconData,
                                    ),
                                  ),
                                ),
                              ),
                        ],
                      ),
                    ],
                  );
                } else {
                  // 标准布局：2列网格，多行
                  return Column(
                    children: [
                      for (int i = 0; i < statsData.length; i += 2)
                        Padding(
                          padding: EdgeInsets.only(
                            bottom: i + 2 < statsData.length ? 12 : 0,
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: StatsCard(
                                  title: statsData[i]['title'] as String,
                                  value: statsData[i]['value'] as String,
                                  subtitle: statsData[i]['subtitle'] as String,
                                  color: statsData[i]['color'] as Color,
                                  icon: statsData[i]['icon'] as IconData,
                                ),
                              ),
                              if (i + 1 < statsData.length) ...[
                                const SizedBox(width: 12),
                                Expanded(
                                  child: StatsCard(
                                    title: statsData[i + 1]['title'] as String,
                                    value: statsData[i + 1]['value'] as String,
                                    subtitle:
                                        statsData[i + 1]['subtitle'] as String,
                                    color: statsData[i + 1]['color'] as Color,
                                    icon: statsData[i + 1]['icon'] as IconData,
                                  ),
                                ),
                              ] else
                                const Expanded(child: SizedBox()),
                            ],
                          ),
                        ),
                    ],
                  );
                }
              },
            ),
          ],
        );
      },
    );
  }

  /// 构建快捷操作
  Widget _buildQuickActions() {
    final actions = [
      {
        'title': '具身记忆',
        'subtitle': '五感记忆',
        'icon': Icons.fitness_center,
        'color': const Color(0xFFE03E3E),
        'onTap': () => _startKinestheticMemoryTraining(),
      },
      {
        'title': '时间盒子',
        'subtitle': '专注学习',
        'icon': Icons.timer_outlined,
        'color': const Color(0xFF0F7B6C),
        'onTap': () => context.push('/timebox'),
      },
      {
        'title': '知忆相册',
        'subtitle': '场景记忆',
        'icon': Icons.account_balance,
        'color': const Color(0xFF7C3AED),
        'onTap': () => context.push('/memory-palace'),
      },
      {
        'title': '词汇管理',
        'subtitle': '考研词汇',
        'icon': Icons.library_books,
        'color': const Color(0xFF2E7EED),
        'onTap': () => context.push('/vocabulary-manager'),
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '快捷入口',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 12),
        // 使用 LayoutBuilder 实现响应式布局
        LayoutBuilder(
          builder: (context, constraints) {
            // 根据可用宽度动态计算列数和宽高比
            int columns = 2;
            double aspectRatio = 1.2;
            double spacing = 12;

            // 响应式布局调整 - 适配 iPad 台前调度模式
            if (constraints.maxWidth > 900) {
              // 宽屏设备（iPad 横屏或大窗口）
              columns = 4;
              aspectRatio = 1.0; // 降低宽高比，增加卡片高度
              spacing = 16;
            } else if (constraints.maxWidth > 600) {
              // 中等屏幕（iPad 竖屏或中等窗口）
              columns = 3;
              aspectRatio = 1.1;
              spacing = 14;
            } else if (constraints.maxWidth > 400) {
              // 标准手机屏幕
              columns = 2;
              aspectRatio = 1.2;
              spacing = 12;
            } else {
              // 窄屏或小窗口
              columns = 2;
              aspectRatio = 1.3; // 增加宽高比，适应窄屏
              spacing = 8;
            }

            return GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: columns,
                crossAxisSpacing: spacing,
                mainAxisSpacing: spacing,
                childAspectRatio: aspectRatio,
              ),
              itemCount: actions.length,
              itemBuilder: (context, index) {
                final action = actions[index];
                return QuickActionCard(
                  title: action['title'] as String,
                  subtitle: action['subtitle'] as String,
                  icon: action['icon'] as IconData,
                  color: action['color'] as Color,
                  onTap: action['onTap'] as VoidCallback,
                );
              },
            );
          },
        ),
      ],
    );
  }

  /// 构建浮动操作按钮
  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      heroTag: "home_fab", // 唯一标签避免冲突
      onPressed: _showCreateTaskDialog,
      backgroundColor: const Color(0xFF2E7EED),
      foregroundColor: Colors.white,
      elevation: 2,
      icon: const Icon(Icons.add),
      label: Text(
        AppLocalizations.of(context)!.newLearning,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
    );
  }

  /// 构建调试区域
  Widget _buildDebugSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '🔧 调试工具',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.orange,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _testForceRefresh,
                  icon: const Icon(Icons.refresh, size: 18),
                  label: const Text('强制刷新统计'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _testDataFlow,
                  icon: const Icon(Icons.bug_report, size: 18),
                  label: const Text('测试数据流'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // 雷达图测试按钮
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => context.push('/ability-radar'),
              icon: const Icon(Icons.radar, size: 18),
              label: const Text('🎯 测试雷达图修复效果'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF7C3AED),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 测试强制刷新
  Future<void> _testForceRefresh() async {
    print('🧪 手动测试：开始强制刷新');

    try {
      // 1. 强制刷新学习时间统计服务
      final studyTimeService = ref.read(studyTimeStatisticsServiceProvider);
      print('🔄 手动测试：调用StudyTimeStatisticsService.forceRefresh');
      await studyTimeService.forceRefresh();

      // 2. 刷新时间盒子任务数据
      print('🔄 手动测试：刷新时间盒子任务数据');
      await ref.read(timeBoxProvider.notifier).refresh();

      // 3. 等待数据传播
      await Future.delayed(const Duration(milliseconds: 500));

      print('✅ 手动测试：强制刷新完成');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('强制刷新完成，请查看控制台日志'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      print('❌ 手动测试：强制刷新失败 - $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('强制刷新失败: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  /// 测试数据流
  Future<void> _testDataFlow() async {
    print('🧪 手动测试：开始测试数据流');

    try {
      // 1. 检查服务状态
      final studyTimeService = ref.read(studyTimeStatisticsServiceProvider);
      final timeBoxState = ref.read(timeBoxProvider);
      final studySummary = ref.read(todayStudySummaryProvider);

      print('📊 当前数据状态:');
      print('  - 时间盒子任务数量: ${timeBoxState.tasks.length}');
      print(
        '  - 学习统计服务聚合数据: ${studyTimeService.currentAggregation != null ? "有数据" : "无数据"}',
      );
      print('  - 首页统计摘要: $studySummary');

      // 2. 检查今日任务
      final today = DateTime.now();
      final todayTasks = timeBoxState.tasks.where((task) {
        if (task.startTime == null) return false;
        return task.startTime!.year == today.year &&
            task.startTime!.month == today.month &&
            task.startTime!.day == today.day;
      }).toList();

      print('📅 今日任务详情:');
      for (final task in todayTasks) {
        print(
          '  - ${task.title}: 状态=${task.status}, 开始=${task.startTime}, 结束=${task.endTime}',
        );
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('数据流测试完成，请查看控制台日志'),
            backgroundColor: Colors.blue,
          ),
        );
      }
    } catch (e) {
      print('❌ 手动测试：数据流测试失败 - $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('数据流测试失败: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  /// 处理下拉刷新（显示用户反馈）
  Future<void> _handleRefresh() async {
    try {
      print('🔄 首页刷新：开始刷新数据');

      // 调用静默刷新方法
      await _performSilentRefresh();

      print('✅ 首页刷新：所有数据刷新完成');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.dataUpdated),
            backgroundColor: const Color(0xFF2E7EED), // 统一为品牌主色调
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      print('❌ 首页刷新失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('刷新失败: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// 静默后台刷新（无用户界面反馈）
  Future<void> _performSilentRefresh() async {
    try {
      // 防止频繁刷新：如果距离上次刷新不足5秒，则跳过
      final now = DateTime.now();
      if (_lastSilentRefreshTime != null &&
          now.difference(_lastSilentRefreshTime!).inSeconds < 5) {
        print('🔄 首页静默刷新：距离上次刷新时间过短，跳过本次刷新');
        return;
      }

      _lastSilentRefreshTime = now;
      print('🔄 首页静默刷新：开始后台刷新数据');

      // 1. 刷新时间盒子任务数据
      await ref.read(timeBoxProvider.notifier).refresh();
      print('✅ 首页静默刷新：时间盒子任务数据已刷新');

      // 2. 刷新学习时间统计服务
      final studyTimeService = ref.read(studyTimeStatisticsServiceProvider);
      await studyTimeService.refresh();
      print('✅ 首页静默刷新：学习时间统计数据已刷新');

      // 3. 强制重新计算聚合数据
      final timeBoxState = ref.read(timeBoxProvider);
      if (!timeBoxState.isLoading && timeBoxState.error == null) {
        await studyTimeService.updateFromTimeBoxTasks(timeBoxState.tasks);
        print('✅ 首页静默刷新：聚合数据已重新计算');
      }

      // 4. 等待一小段时间确保数据传播
      await Future.delayed(const Duration(milliseconds: 500));

      print('✅ 首页静默刷新：所有数据刷新完成');
    } catch (e) {
      // 静默处理错误，只记录日志，不显示用户界面
      print('❌ 首页静默刷新失败: $e');
    }
  }

  /// 启动动觉五感记忆训练
  void _startKinestheticMemoryTraining() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(24),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题栏
              Row(
                children: [
                  const Expanded(
                    child: Text(
                      '🧠 具身记忆训练',
                      style: TextStyle(
                        color: Color(0xFF37352F),
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => context.pop(),
                    icon: const Icon(Icons.close, color: Color(0xFF9B9A97)),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              const Text(
                '选择记忆训练模式：',
                style: TextStyle(color: Color(0xFF787774), fontSize: 16),
              ),
              const SizedBox(height: 24),
              _buildTrainingModeOption(
                '🏋️ 集中训练',
                '从考研词汇中选择单词进行30-60分钟的具身记忆训练',
                () => _startFocusedTraining(),
              ),
              const SizedBox(height: 16),
              _buildTrainingModeOption(
                '📚 动作库管理',
                '浏览、筛选和收藏动作，管理个人动作库',
                () => _startActionLibraryManagement(),
              ),
              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建训练模式选项
  Widget _buildTrainingModeOption(
    String title,
    String description,
    VoidCallback onTap,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFFF7F6F3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: const Color(0xFFE3E2DE)),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF37352F),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF787774),
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Color(0xFF9B9A97),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 启动集中训练
  void _startFocusedTraining() {
    // 关闭底部模态窗口
    context.pop();
    // 导航到集中训练页面
    context.push('/focused-training');
  }

  /// 启动动作库管理
  void _startActionLibraryManagement() {
    // 关闭底部模态窗口
    context.pop();
    // 导航到动作库页面
    context.push('/exercise');
  }

  /// 显示创建任务对话框
  void _showCreateTaskDialog() {
    showDialog(
      context: context,
      builder: (context) => TaskCreateDialog(
        onTaskCreated: (task) async {
          // 首先保存任务到TimeBox Provider
          await ref.read(timeBoxProvider.notifier).addTask(task);

          // 任务创建成功后跳转到时间盒子页面
          if (context.mounted) {
            context.push('/timebox');

            // 显示成功提示
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('任务时间盒子创建成功！'),
                backgroundColor: Color(0xFF0F7B6C),
                duration: Duration(milliseconds: 300),
              ),
            );
          }
        },
      ),
    );
  }
}

/// 统计卡片组件
class StatsCard extends StatelessWidget {
  final String title;
  final String value;
  final String subtitle;
  final Color color;
  final IconData icon;
  final VoidCallback? onTap;

  const StatsCard({
    super.key,
    required this.title,
    required this.value,
    required this.subtitle,
    required this.color,
    required this.icon,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 根据可用空间动态调整组件尺寸
        final cardWidth = constraints.maxWidth;

        // 动态计算尺寸
        double iconSize = 18;
        double iconContainerSize = 32;
        double titleFontSize = 12;
        double valueFontSize = 20;
        double subtitleFontSize = 12;
        double padding = 16;
        double spacing = 12;

        // 根据卡片宽度调整尺寸
        if (cardWidth < 120) {
          // 小卡片
          iconSize = 16;
          iconContainerSize = 28;
          titleFontSize = 10;
          valueFontSize = 16;
          subtitleFontSize = 10;
          padding = 12;
          spacing = 8;
        } else if (cardWidth > 200) {
          // 大卡片
          iconSize = 20;
          iconContainerSize = 36;
          titleFontSize = 14;
          valueFontSize = 24;
          subtitleFontSize = 14;
          padding = 20;
          spacing = 16;
        }

        return GestureDetector(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.all(padding),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.04),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Container(
                      width: iconContainerSize,
                      height: iconContainerSize,
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(icon, size: iconSize, color: color),
                    ),
                    const Spacer(),
                    Flexible(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: titleFontSize,
                          color: const Color(0xFF787774),
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: spacing),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: valueFontSize,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF37352F),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: spacing * 0.3),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: subtitleFontSize,
                    color: const Color(0xFF9B9A97),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

/// 快捷操作卡片组件
class QuickActionCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  const QuickActionCard({
    super.key,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 根据可用空间动态调整组件尺寸
        final cardWidth = constraints.maxWidth;

        // 动态计算图标尺寸和字体大小
        double iconSize = 24;
        double titleFontSize = 14;
        double subtitleFontSize = 12;
        double iconContainerSize = 48;
        double padding = 16;
        double spacing = 12;

        // 根据卡片尺寸调整组件大小
        if (cardWidth < 120) {
          // 小卡片（窄屏或多列布局）
          iconSize = 20;
          titleFontSize = 12;
          subtitleFontSize = 10;
          iconContainerSize = 40;
          padding = 12;
          spacing = 8;
        } else if (cardWidth > 180) {
          // 大卡片（宽屏或少列布局）
          iconSize = 28;
          titleFontSize = 16;
          subtitleFontSize = 14;
          iconContainerSize = 56;
          padding = 20;
          spacing = 16;
        }

        return GestureDetector(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.all(padding),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.04),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 图标容器
                Flexible(
                  flex: 2,
                  child: Container(
                    width: iconContainerSize,
                    height: iconContainerSize,
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(icon, size: iconSize, color: color),
                  ),
                ),
                SizedBox(height: spacing),
                // 标题文本
                Flexible(
                  flex: 1,
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: titleFontSize,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF37352F),
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(height: spacing * 0.3),
                // 副标题文本
                Flexible(
                  flex: 1,
                  child: Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: subtitleFontSize,
                      color: const Color(0xFF787774),
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

/// 学习计划条目组件
class LearningPlanItem extends ConsumerWidget {
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final DailyPlanType type;
  final VoidCallback? onTap;

  const LearningPlanItem({
    super.key,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.type,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final today = DateTime.now();
    final hasContent = ref.watch(
      dailyPlanHasContentProvider((date: today, type: type)),
    );

    return GestureDetector(
      onTap: onTap ?? () => _showEditDialog(context, ref, today),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFFF7F6F3),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: const Color(0xFFE3E2DE)),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(icon, size: 20, color: color),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF37352F),
                        ),
                      ),
                      if (hasContent) ...[
                        const SizedBox(width: 8),
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: color,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    hasContent
                        ? '已设置${type == DailyPlanType.planning ? '计划' : '优化'}'
                        : description,
                    style: TextStyle(
                      fontSize: 12,
                      color: hasContent ? color : const Color(0xFF787774),
                      fontWeight: hasContent
                          ? FontWeight.w500
                          : FontWeight.normal,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.chevron_right, size: 20, color: Color(0xFF9B9A97)),
          ],
        ),
      ),
    );
  }

  /// 显示编辑对话框
  void _showEditDialog(BuildContext context, WidgetRef ref, DateTime date) {
    final currentContent = ref.read(
      dailyPlanContentProvider((date: date, type: type)),
    );
    String content = currentContent;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text(
          title,
          style: const TextStyle(
            color: Color(0xFF37352F),
            fontWeight: FontWeight.w600,
          ),
        ),
        content: TextField(
          controller: TextEditingController(text: currentContent),
          onChanged: (value) => content = value,
          maxLines: 8,
          decoration: InputDecoration(
            hintText: type == DailyPlanType.planning
                ? '输入今日学习计划...\n例如：\n• 复习算法题 2小时\n• 背诵英语单词 100个\n• 完成数学练习题'
                : '输入今日学习优化总结...\n例如：\n• 算法理解更深入了\n• 需要加强英语听力\n• 数学解题速度有提升',
            border: const OutlineInputBorder(),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: color),
            ),
          ),
          style: const TextStyle(color: Color(0xFF37352F)),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消', style: TextStyle(color: Color(0xFF787774))),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();

              // 保存数据
              await ref
                  .read(dailyPlanProvider.notifier)
                  .savePlan(date: date, type: type, content: content);

              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('$title已保存'),
                    backgroundColor: color,
                    duration: const Duration(seconds: 2),
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: color,
              foregroundColor: Colors.white,
            ),
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }
}
